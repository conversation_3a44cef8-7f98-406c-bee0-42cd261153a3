#!/usr/bin/env python3
"""
NEUROGLYPH External Patch Engine Validation
Test del sistema di patching su repository GitHub reali

Obiettivi:
- Scaricare 100 file Python da repository popolari
- Introdurre errori controllati (import, sintassi, semantici)
- Testare NGAdaptivePatcher vs altri tool (autopep8, pylint)
- Misurare metriche: True Positive, False Positive, Coverage, Speed
"""

import os
import sys
import glob
import json
import time
import tempfile
import subprocess
import shutil
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass, asdict
import requests
import zipfile
import ast
import random

# Aggiungi path NEUROGLYPH
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from neuroglyph.cognitive.adaptive_patcher import NGAdaptivePatcher
    from neuroglyph.cognitive.validation_structures import ValidationResult, ValidationError
    from neuroglyph.cognitive.sandbox import NGSandbox
    from neuroglyph.core.parser.ng_parser import NGParser
    NEUROGLYPH_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ NEUROGLYPH non disponibile: {e}")
    NEUROGLYPH_AVAILABLE = False


@dataclass
class ErrorInjection:
    """Rappresenta un errore iniettato nel codice."""
    error_type: str  # 'import', 'syntax', 'semantic'
    original_line: str
    modified_line: str
    line_number: int
    description: str


@dataclass
class PatchTestResult:
    """Risultato del test di patching."""
    file_path: str
    error_injection: ErrorInjection
    patch_applied: bool
    patch_correct: bool
    compilation_success: bool
    execution_success: bool
    patch_time: float
    error_message: Optional[str] = None
    patch_content: Optional[str] = None


class ExternalPatchValidator:
    """Validatore esterno per il sistema di patching NEUROGLYPH."""
    
    def __init__(self, work_dir: str = None):
        self.work_dir = Path(work_dir) if work_dir else Path(tempfile.mkdtemp(prefix="ng_patch_test_"))
        self.work_dir.mkdir(exist_ok=True)
        
        # Repository GitHub popolari per test
        self.test_repositories = [
            {
                'name': 'requests',
                'url': 'https://github.com/psf/requests/archive/refs/heads/main.zip',
                'python_files_pattern': 'requests-main/src/requests/*.py'
            },
            {
                'name': 'flask',
                'url': 'https://github.com/pallets/flask/archive/refs/heads/main.zip',
                'python_files_pattern': 'flask-main/src/flask/*.py'
            },
            {
                'name': 'django-utils',
                'url': 'https://github.com/django/django/archive/refs/heads/main.zip',
                'python_files_pattern': 'django-main/django/utils/*.py'
            }
        ]
        
        if NEUROGLYPH_AVAILABLE:
            from neuroglyph.cognitive.sandbox_structures import SandboxConfig, ResourceLimits

            # Configura sandbox con limiti appropriati per patch testing
            resource_limits = ResourceLimits(
                max_memory_mb=256,
                max_execution_time=10.0,
                allow_file_read=True,
                allow_file_write=True,
                allow_imports=True
            )
            config = SandboxConfig(
                default_resource_limits=resource_limits,
                default_timeout=10.0,
                default_memory_limit=256
            )

            self.patcher = NGAdaptivePatcher(db_path=':memory:')
            self.parser = NGParser()
            self.sandbox = NGSandbox(config=config)
        
        print(f"📁 Working directory: {self.work_dir}")
    
    def download_repositories(self) -> List[Path]:
        """Scarica repository di test."""
        print("📥 Downloading test repositories...")
        downloaded_files = []
        
        for repo in self.test_repositories:
            try:
                print(f"  📦 Downloading {repo['name']}...")
                
                # Download ZIP
                response = requests.get(repo['url'], timeout=30)
                response.raise_for_status()
                
                zip_path = self.work_dir / f"{repo['name']}.zip"
                with open(zip_path, 'wb') as f:
                    f.write(response.content)
                
                # Extract
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(self.work_dir)
                
                # Find Python files
                pattern = str(self.work_dir / repo['python_files_pattern'])
                py_files = glob.glob(pattern)
                
                # Limit to 10 files per repository
                selected_files = py_files[:10]
                downloaded_files.extend([Path(f) for f in selected_files])
                
                print(f"    ✅ {len(selected_files)} files from {repo['name']}")
                
            except Exception as e:
                print(f"    ❌ Failed to download {repo['name']}: {e}")
        
        print(f"📊 Total files downloaded: {len(downloaded_files)}")
        return downloaded_files
    
    def inject_error(self, file_path: Path, error_type: str) -> Tuple[str, ErrorInjection]:
        """Inietta un errore controllato nel file."""
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        if not lines:
            raise ValueError(f"File vuoto: {file_path}")
        
        # Trova una linea appropriata per l'errore
        if error_type == 'import':
            # Rimuovi un import esistente
            for i, line in enumerate(lines):
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    original_line = line
                    modified_line = f"# REMOVED: {line}"
                    lines[i] = modified_line
                    
                    injection = ErrorInjection(
                        error_type='import',
                        original_line=original_line.strip(),
                        modified_line=modified_line.strip(),
                        line_number=i + 1,
                        description=f"Removed import at line {i + 1}"
                    )
                    break
            else:
                # Se non ci sono import, aggiungi un import inesistente
                injection = ErrorInjection(
                    error_type='import',
                    original_line="",
                    modified_line="import nonexistent_module_xyz",
                    line_number=1,
                    description="Added non-existent import"
                )
                lines.insert(0, "import nonexistent_module_xyz\n")
        
        elif error_type == 'syntax':
            # Rimuovi una parentesi chiusa
            for i, line in enumerate(lines):
                if '(' in line and ')' in line:
                    original_line = line
                    modified_line = line.replace(')', '', 1)  # Rimuovi prima parentesi chiusa
                    lines[i] = modified_line
                    
                    injection = ErrorInjection(
                        error_type='syntax',
                        original_line=original_line.strip(),
                        modified_line=modified_line.strip(),
                        line_number=i + 1,
                        description=f"Removed closing parenthesis at line {i + 1}"
                    )
                    break
            else:
                # Fallback: aggiungi parentesi non chiusa
                random_line = random.randint(0, len(lines) - 1)
                original_line = lines[random_line]
                modified_line = lines[random_line].rstrip() + " (\n"
                lines[random_line] = modified_line
                
                injection = ErrorInjection(
                    error_type='syntax',
                    original_line=original_line.strip(),
                    modified_line=modified_line.strip(),
                    line_number=random_line + 1,
                    description=f"Added unclosed parenthesis at line {random_line + 1}"
                )
        
        elif error_type == 'semantic':
            # Usa una variabile non definita
            for i, line in enumerate(lines):
                if '=' in line and not line.strip().startswith('#'):
                    original_line = line
                    # Sostituisci una variabile con una non definita
                    modified_line = line.replace('=', '= undefined_variable_xyz +', 1)
                    lines[i] = modified_line
                    
                    injection = ErrorInjection(
                        error_type='semantic',
                        original_line=original_line.strip(),
                        modified_line=modified_line.strip(),
                        line_number=i + 1,
                        description=f"Added undefined variable at line {i + 1}"
                    )
                    break
            else:
                # Fallback: aggiungi linea con variabile non definita
                injection = ErrorInjection(
                    error_type='semantic',
                    original_line="",
                    modified_line="result = undefined_variable_xyz",
                    line_number=len(lines) + 1,
                    description="Added line with undefined variable"
                )
                lines.append("result = undefined_variable_xyz\n")
        
        else:
            raise ValueError(f"Tipo di errore non supportato: {error_type}")
        
        modified_content = ''.join(lines)
        return modified_content, injection

    def validate_code(self, code: str) -> ValidationResult:
        """Valida il codice e restituisce errori."""
        errors = []

        # Test compilazione
        try:
            ast.parse(code)
        except SyntaxError as e:
            errors.append(ValidationError(
                error_type="syntax",
                message=str(e),
                line_number=e.lineno or 0,
                column_number=e.offset or 0,
                severity="error"
            ))

        # Test import (esecuzione limitata)
        try:
            compile(code, '<string>', 'exec')
        except Exception as e:
            if "No module named" in str(e):
                errors.append(ValidationError(
                    error_type="import",
                    message=str(e),
                    line_number=1,
                    column_number=0,
                    severity="error"
                ))
            elif "not defined" in str(e):
                errors.append(ValidationError(
                    error_type="semantic",
                    message=str(e),
                    line_number=1,
                    column_number=0,
                    severity="error"
                ))

        # Crea ValidationResult con nuova struttura
        from neuroglyph.cognitive.validation_structures import ValidationResult as NGValidationResult
        from neuroglyph.cognitive.validation_structures import ValidationError, ValidationLevel, ValidationSeverity, ValidationErrorType

        result = NGValidationResult()

        # Aggiungi errori al risultato
        for error in errors:
            if error.error_type == "syntax":
                val_error = ValidationError(
                    error_type=ValidationErrorType.AST_PARSE_ERROR,
                    severity=ValidationSeverity.ERROR,
                    level=ValidationLevel.SYNTAX,
                    message=error.message
                )
                result.add_error(val_error)
            elif error.error_type == "semantic":
                val_error = ValidationError(
                    error_type=ValidationErrorType.MISSING_PREMISES,
                    severity=ValidationSeverity.ERROR,
                    level=ValidationLevel.SEMANTIC,
                    message=error.message
                )
                result.add_error(val_error)

        result.metadata = {"injected_error": "validation_test"}
        result.finalize()
        return result

    def test_patch_on_file(self, file_path: Path, error_type: str) -> PatchTestResult:
        """Testa il patching su un singolo file."""
        if not NEUROGLYPH_AVAILABLE:
            return PatchTestResult(
                file_path=str(file_path),
                error_injection=ErrorInjection("", "", "", 0, "NEUROGLYPH not available"),
                patch_applied=False,
                patch_correct=False,
                compilation_success=False,
                execution_success=False,
                patch_time=0.0,
                error_message="NEUROGLYPH not available"
            )

        try:
            # Inietta errore
            modified_code, injection = self.inject_error(file_path, error_type)

            # Valida codice con errore
            start_time = time.perf_counter()
            validation_result = self.validate_code(modified_code)

            if not validation_result.has_errors:
                # Errore non rilevato
                return PatchTestResult(
                    file_path=str(file_path),
                    error_injection=injection,
                    patch_applied=False,
                    patch_correct=False,
                    compilation_success=True,
                    execution_success=False,
                    patch_time=0.0,
                    error_message="Error not detected by validation"
                )

            # Applica patch
            patch_results = self.patcher.patch_validation_errors(validation_result)
            patch_time = time.perf_counter() - start_time

            if not patch_results:
                return PatchTestResult(
                    file_path=str(file_path),
                    error_injection=injection,
                    patch_applied=False,
                    patch_correct=False,
                    compilation_success=False,
                    execution_success=False,
                    patch_time=patch_time,
                    error_message="No patches generated"
                )

            # Applica la prima patch
            best_patch = patch_results[0]
            patched_code = self.apply_patch_to_code(modified_code, best_patch)

            # Verifica se la patch risolve il problema
            patched_validation = self.validate_code(patched_code)
            patch_correct = not patched_validation.has_errors

            # Test compilazione
            compilation_success = False
            try:
                compile(patched_code, '<string>', 'exec')
                compilation_success = True
            except:
                pass

            return PatchTestResult(
                file_path=str(file_path),
                error_injection=injection,
                patch_applied=True,
                patch_correct=patch_correct,
                compilation_success=compilation_success,
                execution_success=compilation_success,  # Semplificato per ora
                patch_time=patch_time,
                patch_content=best_patch.patch_content if hasattr(best_patch, 'patch_content') else str(best_patch)
            )

        except Exception as e:
            return PatchTestResult(
                file_path=str(file_path),
                error_injection=ErrorInjection(error_type, "", "", 0, f"Exception: {e}"),
                patch_applied=False,
                patch_correct=False,
                compilation_success=False,
                execution_success=False,
                patch_time=0.0,
                error_message=str(e)
            )

    def apply_patch_to_code(self, code: str, patch_result) -> str:
        """Applica una patch al codice (implementazione semplificata)."""
        # Per ora, implementazione base - da migliorare
        if hasattr(patch_result, 'patch_content'):
            # Se la patch contiene il codice completo
            return patch_result.patch_content
        else:
            # Fallback: restituisci il codice originale
            return code

    def run_validation_batch(self, max_files: int = 30) -> Dict[str, Any]:
        """Esegue validazione batch su file esterni."""
        print("🚀 Starting External Patch Engine Validation...")

        # Download repository
        files = self.download_repositories()

        if not files:
            print("❌ No files downloaded, aborting test")
            return {"error": "No files downloaded"}

        # Limita numero di file
        test_files = files[:max_files]
        print(f"🎯 Testing on {len(test_files)} files")

        # Tipi di errori da testare
        error_types = ['import', 'syntax', 'semantic']

        results = []

        for i, file_path in enumerate(test_files):
            print(f"📝 Testing file {i+1}/{len(test_files)}: {file_path.name}")

            for error_type in error_types:
                print(f"  🔧 Testing {error_type} error...")
                result = self.test_patch_on_file(file_path, error_type)
                results.append(result)

        # Analizza risultati
        return self.analyze_results(results)

    def analyze_results(self, results: List[PatchTestResult]) -> Dict[str, Any]:
        """Analizza i risultati dei test."""
        total_tests = len(results)

        # Metriche base
        patches_applied = [r for r in results if r.patch_applied]
        patches_correct = [r for r in results if r.patch_correct]
        compilations_success = [r for r in results if r.compilation_success]

        # Metriche per tipo di errore
        by_error_type = {}
        for result in results:
            error_type = result.error_injection.error_type
            if error_type not in by_error_type:
                by_error_type[error_type] = []
            by_error_type[error_type].append(result)

        # Performance
        patch_times = [r.patch_time for r in results if r.patch_time > 0]
        avg_patch_time = sum(patch_times) / len(patch_times) if patch_times else 0

        # Report
        report = {
            'summary': {
                'total_tests': total_tests,
                'patches_applied': len(patches_applied),
                'patches_correct': len(patches_correct),
                'compilations_success': len(compilations_success),
                'patch_application_rate': len(patches_applied) / total_tests if total_tests > 0 else 0,
                'patch_correctness_rate': len(patches_correct) / total_tests if total_tests > 0 else 0,
                'compilation_success_rate': len(compilations_success) / total_tests if total_tests > 0 else 0,
                'avg_patch_time': avg_patch_time
            },
            'by_error_type': {},
            'detailed_results': [asdict(r) for r in results]
        }

        # Analisi per tipo di errore
        for error_type, type_results in by_error_type.items():
            type_applied = [r for r in type_results if r.patch_applied]
            type_correct = [r for r in type_results if r.patch_correct]

            report['by_error_type'][error_type] = {
                'total': len(type_results),
                'applied': len(type_applied),
                'correct': len(type_correct),
                'application_rate': len(type_applied) / len(type_results) if type_results else 0,
                'correctness_rate': len(type_correct) / len(type_results) if type_results else 0
            }

        return report


def main():
    """Funzione principale."""
    import argparse

    parser = argparse.ArgumentParser(description="NEUROGLYPH External Patch Engine Validation")
    parser.add_argument('--max-files', type=int, default=30, help='Maximum number of files to test')
    parser.add_argument('--work-dir', type=str, help='Working directory for downloads')
    parser.add_argument('--output', type=str, default='patch_validation_report.json', help='Output report file')

    args = parser.parse_args()

    # Crea validatore
    validator = ExternalPatchValidator(work_dir=args.work_dir)

    try:
        # Esegui validazione
        results = validator.run_validation_batch(max_files=args.max_files)

        # Salva report
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2)

        # Stampa summary
        if 'summary' in results:
            summary = results['summary']
            print(f"\n📊 EXTERNAL PATCH VALIDATION RESULTS:")
            print(f"   - Total tests: {summary['total_tests']}")
            print(f"   - Patches applied: {summary['patches_applied']} ({summary['patch_application_rate']:.2%})")
            print(f"   - Patches correct: {summary['patches_correct']} ({summary['patch_correctness_rate']:.2%})")
            print(f"   - Compilations successful: {summary['compilations_success']} ({summary['compilation_success_rate']:.2%})")
            print(f"   - Average patch time: {summary['avg_patch_time']:.3f}s")

            # Target validation
            target_correctness = 0.70  # 70% come richiesto
            if summary['patch_correctness_rate'] >= target_correctness:
                print(f"✅ TARGET RAGGIUNTO: {summary['patch_correctness_rate']:.2%} ≥ {target_correctness:.0%}")
            else:
                print(f"❌ TARGET MANCATO: {summary['patch_correctness_rate']:.2%} < {target_correctness:.0%}")

        print(f"📄 Full report saved to: {args.output}")

    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Cleanup
        if validator.work_dir.exists():
            print(f"🧹 Cleaning up {validator.work_dir}")
            shutil.rmtree(validator.work_dir, ignore_errors=True)


if __name__ == '__main__':
    main()
