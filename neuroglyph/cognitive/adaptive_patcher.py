"""
NEUROGLYPH Adaptive Patcher
Sistema di auto-correzione intelligente con apprendimento adattivo
"""

import time
import json
import logging
from typing import List, Dict, Any, Optional, Union
from pathlib import Path

from .performance_storage import PerformanceStorage
from .error_cache import get_error_cache

from .patcher_structures import (
    ErrorAnalysis, PatchCandidate, PatchResult, PatchStatus,
    AdaptiveLearningState, LearningPattern, PatcherConfig
)
from .error_analyzer import <PERSON>rrorAnalyzer
from .patch_generator import PatchGenerator

# Import lazy per evitare cicli
try:
    from ..learning.pattern_extractor import PatternExtractor
    from ..learning.knowledge_graph_builder import KnowledgeGraphBuilder
    LEARNING_AVAILABLE = True
except ImportError:
    LEARNING_AVAILABLE = False
# Import lazy per evitare cicli
# from .validation_structures import ValidationResult
# from .sandbox_structures import ExecutionResult
# from .reasoning_structures import ReasoningGraph
from .sandbox import NGSandbox


class NGAdaptivePatcher:
    """
    Adaptive Patcher per NEUROGLYPH.
    Sistema di auto-correzione intelligente con apprendimento adattivo.
    """
    
    def __init__(self, config: Optional[PatcherConfig] = None, db_path: str = "learning.db"):
        """
        Inizializza l'adaptive patcher con PerformanceStorage.

        Args:
            config: Configurazione patcher
            db_path: Percorso database SQLite per performance storage
        """
        self.config = config or PatcherConfig()

        # Componenti
        self.error_analyzer = ErrorAnalyzer(self.config)
        self.patch_generator = PatchGenerator(self.config)
        self.sandbox = NGSandbox()

        # Performance storage (SQLite con WAL) - FASE 2.3
        self.storage = PerformanceStorage(db_path)

        # Cache per risultati idempotenti (Fase 2.4)
        self.error_cache = get_error_cache()

        # Circuit breaker per protezione overload (Fase 2.4)
        self.circuit_breaker = {
            'failures': 0,
            'last_failure_time': 0,
            'disabled_until': 0,
            'failure_threshold': 20,
            'disable_duration': 60.0  # 60 secondi
        }

        # Learning components (Fase 3.0)
        self.pattern_extractor = None
        self.knowledge_graph = None

        if LEARNING_AVAILABLE:
            try:
                self.pattern_extractor = PatternExtractor(db_path)
                self.knowledge_graph = KnowledgeGraphBuilder(db_path.replace('.db', '_kg.db'))
                print("🧠 Learning components inizializzati")
            except Exception as e:
                print(f"⚠️ Errore inizializzazione learning: {e}")
                # Non possiamo modificare LEARNING_AVAILABLE qui (è globale)

        # Stato apprendimento (legacy per compatibilità)
        self.learning_state = AdaptiveLearningState(
            learning_mode=self.config.learning_mode,
            learning_rate=self.config.learning_rate
        )

        # Storia (legacy)
        self.patch_history = []
        self.success_history = []

        # Carica stato precedente se disponibile
        self._load_learning_state()

        print("🔧 NGAdaptivePatcher inizializzato")
        print(f"   - Learning mode: {self.learning_state.learning_mode}")
        print(f"   - Learning rate: {self.learning_state.learning_rate}")
        print(f"   - Success rate: {self.learning_state.overall_success_rate:.3f}")
        print(f"   - Learned patterns: {len(self.learning_state.learned_patterns)}")
        print(f"   - Performance storage: {db_path}")
    
    def patch_execution_error(self, execution_result) -> Optional[PatchResult]:
        """
        Corregge errore da ExecutionResult.
        
        Args:
            execution_result: Risultato esecuzione con errore
            
        Returns:
            Risultato della patch o None se non applicabile
        """
        if execution_result.success:
            return None
        
        # Analizza errore
        error_analysis = self.error_analyzer.analyze_execution_error(execution_result)
        if not error_analysis:
            return None
        
        # Genera e applica patch
        return self._process_error_analysis(error_analysis, execution_result)
    
    def patch_validation_errors(self, validation_result) -> List[PatchResult]:
        """
        Corregge errori da ValidationResult con flow ottimizzato Fase 2.4.

        Args:
            validation_result: Risultato validazione con errori

        Returns:
            Lista risultati patch
        """
        start_time = time.time()

        # Early exit se nessun errore (O(1) con bitmask)
        if not validation_result.has_errors:
            self.storage.record_metric(latency_ms=0.02, cache_hit=1)
            return []

        # Circuit breaker check
        if self._is_circuit_breaker_active():
            self.storage.record_metric(latency_ms=0.01, cache_hit=0, error_count=0)
            return []

        # Cache lookup
        cached_results = self.error_cache.get(validation_result.digest)
        if cached_results is not None:
            latency_ms = (time.time() - start_time) * 1000
            self.storage.record_metric(latency_ms=latency_ms, cache_hit=1)
            return cached_results

        # Processo normale con analisi errori
        try:
            results = []
            error_analyses = self.error_analyzer.analyze_validation_error(validation_result)

            for error_analysis in error_analyses:
                result = self._process_error_analysis(error_analysis, validation_result)
                if result:
                    results.append(result)

            # Cache risultati
            self.error_cache.put(validation_result.digest, results)

            # Aggiorna circuit breaker
            self._update_circuit_breaker(len(results) > 0)

            # Registra metriche
            latency_ms = (time.time() - start_time) * 1000
            success_rate = sum(1 for r in results if r.success) / max(1, len(results))
            self.storage.record_metric(
                latency_ms=latency_ms,
                cache_hit=0,
                error_count=len(error_analyses),
                success_rate=success_rate
            )

            return results

        except Exception as e:
            # Errore nel processing - aggiorna circuit breaker
            self._update_circuit_breaker(False)
            latency_ms = (time.time() - start_time) * 1000
            self.storage.record_metric(latency_ms=latency_ms, cache_hit=0, error_count=1, success_rate=0.0)
            return []
    
    def patch_reasoning_errors(self, reasoning_graph) -> List[PatchResult]:
        """
        Corregge errori nel reasoning graph.
        
        Args:
            reasoning_graph: Grafo di reasoning con errori
            
        Returns:
            Lista risultati patch
        """
        results = []
        
        # Analizza errori
        error_analyses = self.error_analyzer.analyze_reasoning_error(reasoning_graph)
        
        for error_analysis in error_analyses:
            result = self._process_error_analysis(error_analysis, reasoning_graph)
            if result:
                results.append(result)
        
        return results
    
    def _process_error_analysis(self, error_analysis: ErrorAnalysis, 
                               source_object: Any) -> Optional[PatchResult]:
        """Processa analisi errore e genera patch."""
        start_time = time.time()
        
        # Aggiorna statistiche
        self.learning_state.total_errors_seen += 1
        
        # Genera candidati patch
        candidates = self.patch_generator.generate_patches(error_analysis)
        
        if not candidates:
            return self._create_failed_result(error_analysis, "No patch candidates generated")
        
        self.learning_state.total_patches_generated += len(candidates)
        
        # Prova candidati in ordine di qualità
        for candidate in candidates:
            result = self._apply_patch_candidate(candidate, error_analysis, source_object)
            
            if result.success:
                # Patch riuscita
                self.learning_state.total_patches_successful += 1
                self.learning_state.total_errors_fixed += 1
                
                # Aggiorna apprendimento
                self._update_learning(candidate, result, True)
                
                # Salva storia successi
                self.success_history.append({
                    'error_pattern': error_analysis.error_pattern,
                    'patch_strategy': candidate.patch_strategy,
                    'success_time': time.time()
                })
                
                result.execution_time = time.time() - start_time
                return result
            else:
                # Patch fallita, continua con prossimo candidato
                self._update_learning(candidate, result, False)
        
        # Nessuna patch riuscita
        return self._create_failed_result(error_analysis, "All patch candidates failed")
    
    def _apply_patch_candidate(self, candidate: PatchCandidate, 
                              error_analysis: ErrorAnalysis,
                              source_object: Any) -> PatchResult:
        """Applica candidato patch."""
        result = PatchResult(
            patch_id=candidate.patch_id,
            error_id=error_analysis.error_id
        )
        
        try:
            # Valida patch se abilitato
            if self.config.enable_patch_validation:
                validation_passed = self._validate_patch(candidate)
                result.validation_passed = validation_passed
                
                if not validation_passed and candidate.risk_level in ["high", "critical"]:
                    result.error_message = "Patch validation failed for high-risk patch"
                    return result
            
            # Applica patch basata su tipo (import lazy per evitare cicli)
            from .sandbox_structures import ExecutionResult
            from .validation_structures import ValidationResult
            from .reasoning_structures import ReasoningGraph

            if isinstance(source_object, ExecutionResult):
                success = self._apply_execution_patch(candidate, source_object, result)
            elif isinstance(source_object, ValidationResult):
                success = self._apply_validation_patch(candidate, source_object, result)
            elif isinstance(source_object, ReasoningGraph):
                success = self._apply_reasoning_patch(candidate, source_object, result)
            else:
                result.error_message = f"Unsupported source object type: {type(source_object)}"
                return result
            
            result.success = success
            result.status = PatchStatus.SUCCESSFUL if success else PatchStatus.FAILED
            
        except Exception as e:
            result.error_message = f"Patch application failed: {str(e)}"
            result.status = PatchStatus.FAILED
        
        # Aggiorna storia (legacy)
        self.patch_history.append({
            'patch_id': candidate.patch_id,
            'error_id': error_analysis.error_id,
            'success': result.success,
            'timestamp': time.time()
        })

        # Aggiorna PerformanceStorage (FASE 2.3)
        self.storage.append_patch_history(
            error_type=error_analysis.error_pattern or "unknown",
            status="successful" if result.success else "failed",
            metadata={
                'patch_id': candidate.patch_id,
                'error_id': error_analysis.error_id,
                'patch_strategy': candidate.patch_strategy,
                'patch_type': candidate.patch_type.value,
                'confidence': candidate.confidence,
                'risk_level': candidate.risk_level,
                'execution_time': result.execution_time,
                'memory_usage': result.memory_usage
            }
        )
        
        return result
    
    def _apply_execution_patch(self, candidate: PatchCandidate,
                              execution_result,
                              result: PatchResult) -> bool:
        """Applica patch per errore di esecuzione."""
        if not execution_result.execution_context:
            return False
        
        original_code = execution_result.execution_context.code
        
        # Genera codice corretto basato su strategia patch
        patched_code = self._generate_patched_code(candidate, original_code)
        
        if not patched_code or patched_code == original_code:
            result.error_message = "Failed to generate patched code"
            return False
        
        # Testa codice corretto nel sandbox
        try:
            test_result = self.sandbox.execute_code(
                patched_code,
                timeout=self.config.validation_timeout,
                security_level=execution_result.execution_context.security_level
            )
            
            result.output = test_result.result
            result.execution_time = test_result.duration
            result.memory_usage = test_result.resource_usage.peak_memory_mb
            
            # Successo se esecuzione completata senza errori
            success = test_result.success and not test_result.error_message
            
            if success:
                # Calcola miglioramento performance
                if execution_result.duration > 0:
                    improvement = (execution_result.duration - test_result.duration) / execution_result.duration
                    result.performance_improvement = max(0.0, improvement * 100)
            
            return success
            
        except Exception as e:
            result.error_message = f"Patch testing failed: {str(e)}"
            return False
    
    def _apply_validation_patch(self, candidate: PatchCandidate,
                               validation_result,
                               result: PatchResult) -> bool:
        """Applica patch per errore di validazione."""
        # Per errori di validazione, simula miglioramento
        # In implementazione reale, modificherebbe il grafo di reasoning
        
        result.output = "Validation patch applied"
        result.quality_improvement = candidate.confidence * 50  # Stima miglioramento
        
        return candidate.confidence > 0.5
    
    def _apply_reasoning_patch(self, candidate: PatchCandidate,
                              reasoning_graph,
                              result: PatchResult) -> bool:
        """Applica patch per errore di reasoning."""
        # Per errori di reasoning, simula correzione
        # In implementazione reale, modificherebbe il grafo
        
        if "contradiction" in candidate.patch_description.lower():
            # Simula risoluzione contraddizione
            result.output = "Contradiction resolved"
            result.quality_improvement = 30.0
            return True
        elif "quality" in candidate.patch_description.lower():
            # Simula miglioramento qualità
            result.output = "Reasoning quality improved"
            result.quality_improvement = 20.0
            return True
        
        return False
    
    def _generate_patched_code(self, candidate: PatchCandidate, original_code: str) -> str:
        """Genera codice corretto basato su patch con pattern intelligenti."""
        strategy = candidate.patch_strategy

        if strategy == "variable_initialization":
            var_name = candidate.metadata.get("variable_name", "x")
            # Aggiungi inizializzazione variabile all'inizio
            return f"{var_name} = None  # Auto-generated initialization\n{original_code}"

        elif strategy == "import_addition":
            module_name = candidate.metadata.get("module_name", "math")
            # Aggiungi import all'inizio del file, non duplicare
            lines = original_code.split('\n')
            import_line = f"import {module_name}"

            # Controlla se import già presente
            if any(import_line in line for line in lines):
                return original_code

            # Trova posizione corretta per import (dopo docstring/commenti)
            insert_pos = 0
            for i, line in enumerate(lines):
                stripped = line.strip()
                if stripped and not stripped.startswith('#') and not stripped.startswith('"""') and not stripped.startswith("'''"):
                    insert_pos = i
                    break

            lines.insert(insert_pos, import_line)
            return '\n'.join(lines)

        elif strategy == "attribute_check":
            attr_name = candidate.metadata.get("attribute_name", "attr")
            obj_name = candidate.metadata.get("object_name", "obj")
            # Aggiungi check attributo reale
            lines = original_code.split('\n')

            # Trova linea con errore AttributeError
            for i, line in enumerate(lines):
                if f"{obj_name}.{attr_name}" in line:
                    # Sostituisci con getattr safe
                    safe_line = line.replace(
                        f"{obj_name}.{attr_name}",
                        f"getattr({obj_name}, '{attr_name}', None)"
                    )
                    lines[i] = safe_line
                    break

            return '\n'.join(lines)

        elif strategy == "syntax_repair":
            return self._advanced_syntax_repair(original_code, candidate.metadata)

        elif strategy == "name_error_fix":
            return self._fix_name_error(original_code, candidate.metadata)

        elif strategy == "indentation_fix":
            return self._fix_indentation(original_code, candidate.metadata)

        elif strategy == "timeout_adjustment":
            # Per timeout, non modifica codice ma suggerisce aumento limite
            return original_code

        # Default: ritorna codice originale
        return original_code

    def _advanced_syntax_repair(self, code: str, metadata: dict) -> str:
        """Riparazione sintassi avanzata."""
        patched = code

        # 1. Aggiungi due punti mancanti
        lines = patched.split('\n')
        for i, line in enumerate(lines):
            stripped = line.strip()
            keywords = ["if ", "for ", "while ", "def ", "class ", "elif ", "else", "try:", "except", "finally:"]

            for kw in keywords:
                if stripped.startswith(kw) and not stripped.endswith(':') and kw != "else" and kw != "try:" and kw != "finally:":
                    lines[i] = line + ':'
                    break

        # 2. Fix parentesi non bilanciate
        patched = '\n'.join(lines)
        open_parens = patched.count('(')
        close_parens = patched.count(')')
        if open_parens > close_parens:
            patched += ')' * (open_parens - close_parens)

        # 3. Fix virgolette non chiuse
        single_quotes = patched.count("'") - patched.count("\\'")
        if single_quotes % 2 == 1:
            patched += "'"

        double_quotes = patched.count('"') - patched.count('\\"')
        if double_quotes % 2 == 1:
            patched += '"'

        return patched

    def _fix_name_error(self, code: str, metadata: dict) -> str:
        """Fix NameError comuni."""
        undefined_name = metadata.get("undefined_name", "")
        if not undefined_name:
            return code

        # Pattern comuni di fix
        common_fixes = {
            "math": "import math",
            "os": "import os",
            "sys": "import sys",
            "re": "import re",
            "json": "import json",
            "time": "import time",
            "random": "import random",
            "numpy": "import numpy as np",
            "np": "import numpy as np",
            "pd": "import pandas as pd",
            "plt": "import matplotlib.pyplot as plt"
        }

        if undefined_name in common_fixes:
            import_line = common_fixes[undefined_name]
            return f"{import_line}\n{code}"

        # Fallback: definisci variabile come None
        return f"{undefined_name} = None  # Auto-generated fix\n{code}"

    def _fix_indentation(self, code: str, metadata: dict) -> str:
        """Fix indentazione."""
        lines = code.split('\n')
        fixed_lines = []

        for line in lines:
            # Normalizza indentazione a 4 spazi
            stripped = line.lstrip()
            if stripped:
                # Conta livello indentazione originale
                indent_level = (len(line) - len(stripped)) // 4
                fixed_line = '    ' * indent_level + stripped
                fixed_lines.append(fixed_line)
            else:
                fixed_lines.append('')

        return '\n'.join(fixed_lines)
    
    def _validate_patch(self, candidate: PatchCandidate) -> bool:
        """Valida candidato patch."""
        # Validazione basica
        if candidate.confidence < self.config.min_confidence_threshold:
            return False
        
        if candidate.risk_level == "critical" and not self.config.allow_risky_patches:
            return False
        
        return True

    def _is_circuit_breaker_active(self) -> bool:
        """Verifica se circuit breaker è attivo."""
        current_time = time.time()
        return current_time < self.circuit_breaker['disabled_until']

    def _update_circuit_breaker(self, success: bool):
        """Aggiorna stato circuit breaker."""
        current_time = time.time()

        if success:
            # Reset su successo
            self.circuit_breaker['failures'] = 0
        else:
            # Incrementa fallimenti
            self.circuit_breaker['failures'] += 1
            self.circuit_breaker['last_failure_time'] = current_time

            # Attiva circuit breaker se soglia superata
            if self.circuit_breaker['failures'] >= self.circuit_breaker['failure_threshold']:
                self.circuit_breaker['disabled_until'] = current_time + self.circuit_breaker['disable_duration']
                print(f"🚨 Circuit breaker attivato per {self.circuit_breaker['disable_duration']}s")

    def _update_learning(self, candidate: PatchCandidate, result: PatchResult, success: bool):
        """Aggiorna apprendimento basato su risultato patch con Fase 3.0 learning."""
        # Legacy learning (compatibilità)
        pattern_name = f"{candidate.patch_strategy}_{candidate.patch_type.value}"

        # Fase 3.0: Aggiorna Knowledge Graph
        if self.knowledge_graph and success:
            try:
                error_type = candidate.metadata.get("error_pattern", "unknown")
                self.knowledge_graph.create_error_patch_pattern_chain(
                    error_type=error_type,
                    patch_id=candidate.patch_id,
                    pattern_id=pattern_name
                )
            except Exception as e:
                print(f"⚠️ Errore aggiornamento KG: {e}")

        # Fase 3.0: Pattern Extractor viene aggiornato automaticamente via thread
        # (non serve chiamata esplicita qui, il thread estrae pattern ogni 15 min)
        
        # Cerca pattern esistente
        existing_pattern = None
        for pattern in self.learning_state.learned_patterns:
            if pattern.pattern_name == pattern_name:
                existing_pattern = pattern
                break
        
        if existing_pattern:
            # Aggiorna pattern esistente
            existing_pattern.occurrence_count += 1
            if success:
                existing_pattern.success_count += 1
            else:
                existing_pattern.failure_count += 1
            
            # Ricalcola effectiveness
            total_attempts = existing_pattern.success_count + existing_pattern.failure_count
            existing_pattern.effectiveness_score = existing_pattern.success_count / total_attempts
            existing_pattern.last_updated = time.time()
        else:
            # Crea nuovo pattern
            new_pattern = LearningPattern(
                pattern_name=pattern_name,
                error_pattern=candidate.metadata.get("error_pattern", "unknown"),
                solution_pattern=candidate.patch_strategy,
                occurrence_count=1,
                success_count=1 if success else 0,
                failure_count=0 if success else 1,
                effectiveness_score=1.0 if success else 0.0
            )
            self.learning_state.learned_patterns.append(new_pattern)

            # Aggiorna PerformanceStorage con nuovo pattern (FASE 2.3)
            self.storage.append_learned_pattern(
                pattern=pattern_name,
                metadata={
                    'error_pattern': new_pattern.error_pattern,
                    'solution_pattern': new_pattern.solution_pattern,
                    'occurrence_count': new_pattern.occurrence_count,
                    'success_count': new_pattern.success_count,
                    'failure_count': new_pattern.failure_count
                },
                effectiveness=new_pattern.effectiveness_score
            )

        # Aggiorna stato generale
        self.learning_state.update_success_rate()

        # Adattamento se necessario
        if self.learning_state.should_adapt():
            self._adapt_learning()
    
    def _adapt_learning(self):
        """Adatta parametri di apprendimento."""
        print("🔧 Adapting learning parameters...")
        
        # Aggiusta learning rate basato su performance
        if self.learning_state.overall_success_rate < 0.3:
            # Performance bassa, aumenta learning rate
            self.learning_state.learning_rate = min(0.5, self.learning_state.learning_rate * 1.2)
        elif self.learning_state.overall_success_rate > 0.8:
            # Performance alta, diminuisci learning rate
            self.learning_state.learning_rate = max(0.01, self.learning_state.learning_rate * 0.9)
        
        # Rimuovi pattern inefficaci
        self.learning_state.learned_patterns = [
            p for p in self.learning_state.learned_patterns
            if p.effectiveness_score > 0.1 or p.occurrence_count < 5
        ]
        
        self.learning_state.last_adaptation = time.time()
        
        # Salva stato
        self._save_learning_state()
    
    def _create_failed_result(self, error_analysis: ErrorAnalysis, reason: str) -> PatchResult:
        """Crea risultato fallito."""
        return PatchResult(
            error_id=error_analysis.error_id,
            success=False,
            status=PatchStatus.FAILED,
            error_message=reason
        )
    
    def _load_learning_state(self):
        """Carica stato apprendimento da file."""
        if not self.config.save_learning_state:
            return
        
        try:
            with open(self.config.learning_state_file, 'r') as f:
                data = json.load(f)
                # Caricamento semplificato - in implementazione reale deserializzerebbe completamente
                self.learning_state.total_errors_seen = data.get('total_errors_seen', 0)
                self.learning_state.total_patches_successful = data.get('total_patches_successful', 0)
                self.learning_state.overall_success_rate = data.get('overall_success_rate', 0.0)
        except (FileNotFoundError, json.JSONDecodeError):
            # File non esiste o corrotto, usa stato default
            pass
    
    def _save_learning_state(self):
        """Salva stato apprendimento su file."""
        if not self.config.save_learning_state:
            return
        
        try:
            data = {
                'total_errors_seen': self.learning_state.total_errors_seen,
                'total_patches_successful': self.learning_state.total_patches_successful,
                'overall_success_rate': self.learning_state.overall_success_rate,
                'learned_patterns_count': len(self.learning_state.learned_patterns)
            }
            
            with open(self.config.learning_state_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"⚠️ Failed to save learning state: {e}")
    
    def get_learning_statistics(self) -> Dict[str, Any]:
        """Ottiene statistiche di apprendimento con PerformanceStorage (hot-path)."""
        # Statistiche hot-path da PerformanceStorage (FASE 2.3)
        storage_stats = self.storage.get_statistics()

        # Combina con statistiche legacy per compatibilità
        return {
            # Hot-path statistics (O(1) da PerformanceStorage)
            'total_patches': storage_stats['total_patches'],
            'successful_patches': storage_stats['successful_patches'],
            'failed_patches': storage_stats['failed_patches'],
            'success_rate': storage_stats['success_rate'],
            'patches_24h': storage_stats['patches_24h'],
            'learned_patterns_count': storage_stats['learned_patterns'],

            # Legacy statistics (per compatibilità)
            'total_errors_seen': self.learning_state.total_errors_seen,
            'total_errors_fixed': self.learning_state.total_errors_fixed,
            'total_patches_generated': self.learning_state.total_patches_generated,
            'total_patches_successful': self.learning_state.total_patches_successful,
            'overall_success_rate': self.learning_state.overall_success_rate,
            'learned_patterns': len(self.learning_state.learned_patterns),
            'learning_rate': self.learning_state.learning_rate,
            'recent_successes': len([s for s in self.success_history if time.time() - s['success_time'] < 3600]),

            # Performance info
            'buffer_sizes': storage_stats['buffer_sizes'],
            'storage_performance': 'optimized',

            # Fase 3.0: Learning statistics
            'learning_available': LEARNING_AVAILABLE,
            'pattern_extractor_stats': self.pattern_extractor.get_stats() if self.pattern_extractor else None,
            'knowledge_graph_stats': self.knowledge_graph.get_summary() if self.knowledge_graph else None
        }

    def get_database_info(self) -> Dict[str, Any]:
        """Ottiene informazioni database per diagnostica."""
        return self.storage.get_database_info()

    def shutdown(self):
        """Shutdown graceful con flush finale."""
        if hasattr(self, 'storage'):
            self.storage.shutdown()
        self._save_learning_state()
